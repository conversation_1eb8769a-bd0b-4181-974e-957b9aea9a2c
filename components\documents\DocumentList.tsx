'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText,
  Image,
  File,
  Download,
  Eye,
  Share2,
  Calendar,
  Building,
  MoreVertical,
  ArrowUpDown
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Document {
  id: string
  name: string
  path: string
  procedureId?: string
  procedureName?: string
  referenceNumber?: string
  dependency?: string
  status?: any
  uploadedAt: string
  size?: number
  type: string
}

interface DocumentListProps {
  documents: Document[]
}

export function DocumentList({ documents }: DocumentListProps) {
  const [loading, setLoading] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'type'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const supabase = createClient()

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />
      case 'image':
        return <Image className="h-5 w-5 text-green-600" />
      case 'document':
        return <FileText className="h-5 w-5 text-blue-600" />
      default:
        return <File className="h-5 w-5 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'bg-red-100 text-red-800'
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'document':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleSort = (field: 'name' | 'date' | 'type') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const sortedDocuments = [...documents].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'date':
        comparison = new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime()
        break
      case 'type':
        comparison = a.type.localeCompare(b.type)
        break
    }
    
    return sortOrder === 'asc' ? comparison : -comparison
  })

  const handleDownload = async (document: Document) => {
    setLoading(document.id)
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      const link = document.createElement('a')
      link.href = data.publicUrl
      link.download = document.name
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
    } finally {
      setLoading(null)
    }
  }

  const handleView = async (document: Document) => {
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      window.open(data.publicUrl, '_blank')
    } catch (error) {
      console.error('Error viewing file:', error)
    }
  }

  const handleShare = async (document: Document) => {
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      if (navigator.share) {
        await navigator.share({
          title: document.name,
          text: `Documento: ${document.name}`,
          url: data.publicUrl
        })
      } else {
        await navigator.clipboard.writeText(data.publicUrl)
      }
    } catch (error) {
      console.error('Error sharing file:', error)
    }
  }

  if (documents.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <File className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay documentos
          </h3>
          <p className="text-gray-500">
            No se encontraron documentos que coincidan con los filtros seleccionados
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0">
        {/* Table Header */}
        <div className="border-b bg-gray-50 px-6 py-3">
          <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
            <div className="col-span-5">
              <button
                onClick={() => handleSort('name')}
                className="flex items-center space-x-1 hover:text-gray-900"
              >
                <span>Documento</span>
                <ArrowUpDown className="h-3 w-3" />
              </button>
            </div>
            <div className="col-span-2">
              <button
                onClick={() => handleSort('type')}
                className="flex items-center space-x-1 hover:text-gray-900"
              >
                <span>Tipo</span>
                <ArrowUpDown className="h-3 w-3" />
              </button>
            </div>
            <div className="col-span-2">Trámite</div>
            <div className="col-span-2">
              <button
                onClick={() => handleSort('date')}
                className="flex items-center space-x-1 hover:text-gray-900"
              >
                <span>Fecha</span>
                <ArrowUpDown className="h-3 w-3" />
              </button>
            </div>
            <div className="col-span-1">Acciones</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {sortedDocuments.map((document) => (
            <div key={document.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="grid grid-cols-12 gap-4 items-center">
                {/* Document Info */}
                <div className="col-span-5">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(document.type)}
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {document.name}
                      </p>
                      {document.size && (
                        <p className="text-xs text-gray-500">
                          {formatFileSize(document.size)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Type */}
                <div className="col-span-2">
                  <Badge className={getFileTypeColor(document.type)}>
                    {document.type.toUpperCase()}
                  </Badge>
                </div>

                {/* Procedure */}
                <div className="col-span-2">
                  {document.procedureName ? (
                    <div>
                      <p className="text-sm text-gray-900 truncate">
                        {document.procedureName}
                      </p>
                      {document.referenceNumber && (
                        <p className="text-xs text-gray-500">
                          {document.referenceNumber}
                        </p>
                      )}
                      {document.dependency && (
                        <p className="text-xs text-gray-500 flex items-center">
                          <Building className="h-3 w-3 mr-1" />
                          {document.dependency}
                        </p>
                      )}
                    </div>
                  ) : (
                    <span className="text-sm text-gray-400">-</span>
                  )}
                </div>

                {/* Date */}
                <div className="col-span-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(document.uploadedAt)}
                  </div>
                </div>

                {/* Actions */}
                <div className="col-span-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleView(document)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Ver
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDownload(document)}
                        disabled={loading === document.id}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Descargar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleShare(document)}>
                        <Share2 className="h-4 w-4 mr-2" />
                        Compartir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {/* Status (if available) */}
              {document.status && (
                <div className="mt-2 ml-8">
                  <Badge 
                    style={{ backgroundColor: document.status.color }}
                    className="text-white text-xs"
                  >
                    {document.status.display_name}
                  </Badge>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
