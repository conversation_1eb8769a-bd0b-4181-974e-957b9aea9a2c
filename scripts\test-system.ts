/**
 * Script de prueba para verificar la configuración del sistema de chatbot
 * Verifica la conectividad con Supabase y la estructura de la base de datos
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Cargar variables de entorno
config({ path: '.env.local' });

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables de entorno faltantes:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false,
  },
});

async function testSystemConfiguration() {
  console.log('🔍 Verificando configuración del sistema de chatbot...\n');

  try {
    // 1. Verificar conexión con Supabase
    console.log('1. Verificando conexión con Supabase...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('procedures')
      .select('count')
      .limit(1);
    
    if (healthError) {
      console.error('❌ Error de conexión:', healthError.message);
      return;
    }
    console.log('✅ Conexión con Supabase exitosa');

    // 2. Verificar tablas principales
    console.log('\n2. Verificando tablas principales...');
    const tables = ['procedures', 'opas', 'procedures_embeddings', 'opas_embeddings', 'chat_conversations', 'chat_messages'];
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.error(`❌ Tabla ${table}: ${error.message}`);
        } else {
          console.log(`✅ Tabla ${table}: ${count} registros`);
        }
      } catch (err) {
        console.error(`❌ Error verificando tabla ${table}:`, err);
      }
    }

    // 3. Verificar extensión vector
    console.log('\n3. Verificando extensión vector...');
    const { data: extensions, error: extError } = await supabase
      .rpc('sql', { 
        query: "SELECT extname, extversion FROM pg_extension WHERE extname = 'vector';" 
      });
    
    if (extError) {
      console.error('❌ Error verificando extensión vector:', extError.message);
    } else if (extensions && extensions.length > 0) {
      console.log(`✅ Extensión vector instalada: v${extensions[0].extversion}`);
    } else {
      console.error('❌ Extensión vector no encontrada');
    }

    // 4. Verificar funciones de búsqueda
    console.log('\n4. Verificando funciones de búsqueda...');
    const functions = ['match_procedures', 'match_opas', 'match_all_content'];
    
    for (const func of functions) {
      try {
        const { data, error } = await supabase
          .rpc('sql', { 
            query: `SELECT proname FROM pg_proc WHERE proname = '${func}';` 
          });
        
        if (error) {
          console.error(`❌ Error verificando función ${func}:`, error.message);
        } else if (data && data.length > 0) {
          console.log(`✅ Función ${func} disponible`);
        } else {
          console.error(`❌ Función ${func} no encontrada`);
        }
      } catch (err) {
        console.error(`❌ Error verificando función ${func}:`, err);
      }
    }

    // 5. Verificar datos de procedimientos y OPAs
    console.log('\n5. Verificando datos disponibles...');
    
    const { data: proceduresData, error: procError } = await supabase
      .from('procedures')
      .select('id, name, dependency_id')
      .limit(5);
    
    if (procError) {
      console.error('❌ Error obteniendo procedimientos:', procError.message);
    } else {
      console.log(`✅ Procedimientos disponibles: ${proceduresData?.length || 0} (muestra)`);
      if (proceduresData && proceduresData.length > 0) {
        console.log('   Ejemplo:', proceduresData[0].name);
      }
    }

    const { data: opasData, error: opasError } = await supabase
      .from('opas')
      .select('id, name, dependency_id')
      .limit(5);
    
    if (opasError) {
      console.error('❌ Error obteniendo OPAs:', opasError.message);
    } else {
      console.log(`✅ OPAs disponibles: ${opasData?.length || 0} (muestra)`);
      if (opasData && opasData.length > 0) {
        console.log('   Ejemplo:', opasData[0].name);
      }
    }

    // 6. Verificar estructura de embeddings
    console.log('\n6. Verificando estructura de embeddings...');
    
    const { data: embeddingStructure, error: embError } = await supabase
      .rpc('sql', { 
        query: `
          SELECT column_name, data_type 
          FROM information_schema.columns 
          WHERE table_name = 'procedures_embeddings' 
          ORDER BY ordinal_position;
        ` 
      });
    
    if (embError) {
      console.error('❌ Error verificando estructura de embeddings:', embError.message);
    } else if (embeddingStructure && embeddingStructure.length > 0) {
      console.log('✅ Estructura de embeddings:');
      embeddingStructure.forEach((col: any) => {
        console.log(`   - ${col.column_name}: ${col.data_type}`);
      });
    }

    // 7. Verificar variables de entorno de OpenAI
    console.log('\n7. Verificando configuración de OpenAI...');
    const openaiKey = process.env.OPENAI_API_KEY;
    const openaiModel = process.env.OPENAI_MODEL;
    const embeddingModel = process.env.OPENAI_EMBEDDING_MODEL;
    
    console.log(`✅ OPENAI_API_KEY: ${openaiKey ? 'Configurada' : 'No configurada'}`);
    console.log(`✅ OPENAI_MODEL: ${openaiModel || 'gpt-4-turbo-preview (default)'}`);
    console.log(`✅ OPENAI_EMBEDDING_MODEL: ${embeddingModel || 'text-embedding-3-small (default)'}`);

    console.log('\n🎉 Verificación del sistema completada!');
    console.log('\n📋 Próximos pasos:');
    console.log('1. Configurar claves reales de OpenAI y Supabase service role');
    console.log('2. Ejecutar procesamiento de embeddings: npm run process-embeddings');
    console.log('3. Probar el chatbot en /chat');

  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
  }
}

// Ejecutar verificación
testSystemConfiguration()
  .then(() => {
    console.log('\n✅ Script de verificación completado');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error en script de verificación:', error);
    process.exit(1);
  });
