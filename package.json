{"name": "chia-tramites-ai", "version": "1.0.0", "description": "Sistema de Atención Ciudadana AI-First para el municipio de Chía", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate-types": "supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID > src/types/supabase.ts", "db:reset": "supabase db reset", "db:migrate": "supabase migration up", "process-embeddings": "tsx scripts/process-embeddings.ts", "process-embeddings:procedures": "tsx scripts/process-embeddings.ts procedures", "process-embeddings:opas": "tsx scripts/process-embeddings.ts opas", "test-system": "tsx scripts/test-system.ts", "test-system-simple": "tsx scripts/test-system-simple.ts", "process-embeddings-mcp": "tsx scripts/process-embeddings-mcp.ts", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "analyze": "ANALYZE=true npm run build", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./reports/lighthouse.html", "lighthouse:ci": "lhci autorun", "bundle-analyzer": "npm run analyze && npx serve .next/analyze", "perf:audit": "npm run lighthouse && npm run analyze", "accessibility:test": "axe http://localhost:3000", "speed:test": "npm run build && npm run start & sleep 10 && npm run lighthouse && kill %1"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/supabase-js": "^2.39.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dotenv": "^17.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.0.4", "openai": "^4.104.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@axe-core/cli": "^4.8.2", "@lhci/cli": "^0.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "lighthouse": "^11.4.0", "webpack-bundle-analyzer": "^4.10.1", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "supabase": "^1.123.4", "tailwindcss": "^3.3.6", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}