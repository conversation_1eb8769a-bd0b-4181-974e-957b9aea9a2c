import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  FileText, 
  MessageSquare, 
  Clock, 
  Shield, 
  Smartphone,
  Users,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-chia-blue-50 to-chia-green-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-chia-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-chia-blue-900">Municipio de Chía</h1>
                <p className="text-sm text-chia-blue-600">Sistema de Atención Ciudadana</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="outline">Iniciar Sesión</Button>
              </Link>
              <Link href="/auth/register">
                <Button className="bg-chia-blue-600 hover:bg-chia-blue-700">
                  Registrarse
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Servicios Municipales
              <span className="block text-chia-blue-600">Digitales e Inteligentes</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Accede a todos los trámites y servicios del Municipio de Chía desde un solo lugar. 
              Con inteligencia artificial, seguimiento en tiempo real y atención 24/7.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="bg-chia-blue-600 hover:bg-chia-blue-700">
                  Comenzar Ahora
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/procedimientos">
                <Button size="lg" variant="outline">
                  Ver Trámites Disponibles
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Por qué elegir nuestro sistema?
            </h2>
            <p className="text-lg text-gray-600">
              Innovación y eficiencia al servicio de la ciudadanía
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-chia-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-6 w-6 text-chia-blue-600" />
                </div>
                <CardTitle>Asistente IA Inteligente</CardTitle>
                <CardDescription>
                  Chatbot con inteligencia artificial que te guía paso a paso en tus trámites
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-chia-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-6 w-6 text-chia-green-600" />
                </div>
                <CardTitle>Disponible 24/7</CardTitle>
                <CardDescription>
                  Realiza tus trámites en cualquier momento, desde cualquier lugar
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>Seguimiento en Tiempo Real</CardTitle>
                <CardDescription>
                  Conoce el estado de tus trámites con actualizaciones automáticas
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Seguridad Garantizada</CardTitle>
                <CardDescription>
                  Tus datos están protegidos con los más altos estándares de seguridad
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Smartphone className="h-6 w-6 text-yellow-600" />
                </div>
                <CardTitle>Multiplataforma</CardTitle>
                <CardDescription>
                  Accede desde tu computador, tablet o celular con la misma experiencia
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Atención Personalizada</CardTitle>
                <CardDescription>
                  Soporte especializado para resolver todas tus dudas y consultas
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Servicios Disponibles
            </h2>
            <p className="text-lg text-gray-600">
              Más de 100 trámites y 700 OPAs disponibles en línea
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-3 h-6 w-6 text-chia-blue-600" />
                  Trámites Municipales
                </CardTitle>
                <CardDescription>
                  Gestiona todos tus trámites oficiales de manera digital
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Licencias de construcción</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Certificados y constancias</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Permisos comerciales</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Y muchos más...</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-3 h-6 w-6 text-chia-green-600" />
                  OPAs (Otras Prestaciones)
                </CardTitle>
                <CardDescription>
                  Servicios adicionales para mejorar tu calidad de vida
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Programas sociales</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Servicios de salud</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Actividades culturales</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">Más de 700 servicios</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-chia-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            ¿Listo para comenzar?
          </h2>
          <p className="text-xl text-chia-blue-100 mb-8">
            Únete a miles de ciudadanos que ya disfrutan de nuestros servicios digitales
          </p>
          <Link href="/auth/register">
            <Button size="lg" className="bg-white text-chia-blue-600 hover:bg-gray-100">
              Crear Cuenta Gratuita
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-chia-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">C</span>
                </div>
                <span className="font-bold">Municipio de Chía</span>
              </div>
              <p className="text-gray-400">
                Sistema de Atención Ciudadana con tecnología de vanguardia para servir mejor a nuestra comunidad.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Contacto</h3>
              <div className="space-y-2 text-gray-400">
                <p>Carrera 11 # 17-25</p>
                <p>Chía, Cundinamarca</p>
                <p>Teléfono: (*************</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Horarios</h3>
              <div className="space-y-2 text-gray-400">
                <p>Lunes a Viernes</p>
                <p>8:00 AM - 5:00 PM</p>
                <p className="text-chia-blue-400 font-medium">Servicios en línea 24/7</p>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Municipio de Chía. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
