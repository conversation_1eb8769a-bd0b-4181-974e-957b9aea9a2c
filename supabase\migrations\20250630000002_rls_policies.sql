-- Row Level Security Policies for Sistema de Atención Ciudadana - Chía
-- Based on PLAN_IMPLEMENTACION_SISTEMA_ATENCION_CIUDADANA.md

-- =============================================
-- ENABLE RLS ON ALL TABLES
-- =============================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE subdependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE administrative_procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE tramite_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE tramite_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_base ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- =============================================
-- HELPER FUNCTIONS FOR RLS
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT r.name
    FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin of specific dependency
CREATE OR REPLACE FUNCTION is_dependency_admin(dep_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid()
    AND r.name IN ('admin', 'super_admin')
    AND (r.name = 'super_admin' OR p.dependency_id = dep_id)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid()
    AND r.name = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- PROFILES TABLE POLICIES
-- =============================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (except role and dependency)
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id)
  WITH CHECK (
    auth.uid() = id AND
    role_id = (SELECT role_id FROM profiles WHERE id = auth.uid()) AND
    dependency_id = (SELECT dependency_id FROM profiles WHERE id = auth.uid())
  );

-- Admins can view profiles in their dependency
CREATE POLICY "Admins can view dependency profiles" ON profiles
  FOR SELECT USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR dependency_id = (SELECT dependency_id FROM profiles WHERE id = auth.uid()))
  );

-- Super admins can manage all profiles
CREATE POLICY "Super admins can manage all profiles" ON profiles
  FOR ALL USING (is_super_admin());

-- =============================================
-- ROLES TABLE POLICIES
-- =============================================

-- All authenticated users can view roles (for UI purposes)
CREATE POLICY "Authenticated users can view roles" ON roles
  FOR SELECT TO authenticated USING (true);

-- Only super admins can manage roles
CREATE POLICY "Super admins can manage roles" ON roles
  FOR ALL USING (is_super_admin());

-- =============================================
-- DEPENDENCIES AND SUBDEPENDENCIES POLICIES
-- =============================================

-- All authenticated users can view active dependencies
CREATE POLICY "Users can view active dependencies" ON dependencies
  FOR SELECT TO authenticated USING (is_active = true);

-- All authenticated users can view active subdependencies
CREATE POLICY "Users can view active subdependencies" ON subdependencies
  FOR SELECT TO authenticated USING (is_active = true);

-- Admins can manage their own dependency
CREATE POLICY "Admins can manage own dependency" ON dependencies
  FOR ALL USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR is_dependency_admin(id))
  );

-- Admins can manage subdependencies of their dependency
CREATE POLICY "Admins can manage own subdependencies" ON subdependencies
  FOR ALL USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR is_dependency_admin(dependency_id))
  );

-- =============================================
-- PROCEDURES POLICIES
-- =============================================

-- All authenticated users can view active procedures
CREATE POLICY "Users can view active procedures" ON procedures
  FOR SELECT TO authenticated USING (status = 'active');

-- Admins can manage procedures of their dependency
CREATE POLICY "Admins can manage dependency procedures" ON procedures
  FOR ALL USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR is_dependency_admin(dependency_id))
  );

-- =============================================
-- ADMINISTRATIVE PROCEDURES POLICIES
-- =============================================

-- All authenticated users can view active administrative procedures
CREATE POLICY "Users can view active admin procedures" ON administrative_procedures
  FOR SELECT TO authenticated USING (status = 'active');

-- Admins can manage administrative procedures of their dependency
CREATE POLICY "Admins can manage dependency admin procedures" ON administrative_procedures
  FOR ALL USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR is_dependency_admin(dependency_id))
  );

-- =============================================
-- TRAMITE INSTANCES POLICIES
-- =============================================

-- Users can view their own tramites
CREATE POLICY "Users can view own tramites" ON tramite_instances
  FOR SELECT USING (auth.uid() = user_id);

-- Users can create tramites for themselves
CREATE POLICY "Users can create own tramites" ON tramite_instances
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own tramites (limited fields)
CREATE POLICY "Users can update own tramites" ON tramite_instances
  FOR UPDATE USING (auth.uid() = user_id)
  WITH CHECK (
    auth.uid() = user_id AND
    -- Users can only update form_data and notes, not status or assignment
    status = (SELECT status FROM tramite_instances WHERE id = tramite_instances.id) AND
    assigned_to = (SELECT assigned_to FROM tramite_instances WHERE id = tramite_instances.id)
  );

-- Admins can view tramites for procedures in their dependency
CREATE POLICY "Admins can view dependency tramites" ON tramite_instances
  FOR SELECT USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR EXISTS (
      SELECT 1 FROM procedures p
      WHERE p.id = procedure_id
      AND is_dependency_admin(p.dependency_id)
    ))
  );

-- Admins can update tramites for procedures in their dependency
CREATE POLICY "Admins can update dependency tramites" ON tramite_instances
  FOR UPDATE USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR EXISTS (
      SELECT 1 FROM procedures p
      WHERE p.id = procedure_id
      AND is_dependency_admin(p.dependency_id)
    ))
  );

-- =============================================
-- TRAMITE STATUS HISTORY POLICIES
-- =============================================

-- Users can view status history of their own tramites
CREATE POLICY "Users can view own tramite history" ON tramite_status_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tramite_instances ti
      WHERE ti.id = tramite_id AND ti.user_id = auth.uid()
    )
  );

-- Admins can view status history of tramites in their dependency
CREATE POLICY "Admins can view dependency tramite history" ON tramite_status_history
  FOR SELECT USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR EXISTS (
      SELECT 1 FROM tramite_instances ti
      JOIN procedures p ON ti.procedure_id = p.id
      WHERE ti.id = tramite_id
      AND is_dependency_admin(p.dependency_id)
    ))
  );

-- Only admins can insert status history records
CREATE POLICY "Admins can create status history" ON tramite_status_history
  FOR INSERT WITH CHECK (
    get_user_role() IN ('admin', 'super_admin') AND
    auth.uid() = changed_by
  );

-- =============================================
-- KNOWLEDGE BASE POLICIES
-- =============================================

-- All authenticated users can view active knowledge base
CREATE POLICY "Users can view knowledge base" ON knowledge_base
  FOR SELECT TO authenticated USING (is_active = true);

-- Only admins can manage knowledge base
CREATE POLICY "Admins can manage knowledge base" ON knowledge_base
  FOR ALL USING (get_user_role() IN ('admin', 'super_admin'));

-- =============================================
-- CHAT POLICIES
-- =============================================

-- Users can view their own conversations
CREATE POLICY "Users can view own conversations" ON chat_conversations
  FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own conversations
CREATE POLICY "Users can create own conversations" ON chat_conversations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own conversations
CREATE POLICY "Users can update own conversations" ON chat_conversations
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can view messages from their own conversations
CREATE POLICY "Users can view own messages" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_conversations cc
      WHERE cc.id = conversation_id AND cc.user_id = auth.uid()
    )
  );

-- Users can create messages in their own conversations
CREATE POLICY "Users can create own messages" ON chat_messages
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_conversations cc
      WHERE cc.id = conversation_id AND cc.user_id = auth.uid()
    )
  );

-- =============================================
-- DOCUMENT ATTACHMENTS POLICIES
-- =============================================

-- Users can view attachments of their own tramites
CREATE POLICY "Users can view own attachments" ON document_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tramite_instances ti
      WHERE ti.id = tramite_id AND ti.user_id = auth.uid()
    )
  );

-- Users can upload attachments to their own tramites
CREATE POLICY "Users can upload own attachments" ON document_attachments
  FOR INSERT WITH CHECK (
    auth.uid() = uploaded_by AND
    EXISTS (
      SELECT 1 FROM tramite_instances ti
      WHERE ti.id = tramite_id AND ti.user_id = auth.uid()
    )
  );

-- Admins can view attachments of tramites in their dependency
CREATE POLICY "Admins can view dependency attachments" ON document_attachments
  FOR SELECT USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR EXISTS (
      SELECT 1 FROM tramite_instances ti
      JOIN procedures p ON ti.procedure_id = p.id
      WHERE ti.id = tramite_id
      AND is_dependency_admin(p.dependency_id)
    ))
  );

-- =============================================
-- AUDIT LOG POLICIES
-- =============================================

-- Only super admins can view audit logs
CREATE POLICY "Super admins can view audit logs" ON audit_log
  FOR SELECT USING (is_super_admin());

-- System can insert audit logs (no user restriction)
CREATE POLICY "System can insert audit logs" ON audit_log
  FOR INSERT WITH CHECK (true);
