export { ResourceSummaryComputed as ResourceSummary };
export type ResourceEntry = {
    count: number;
    resourceSize: number;
    transferSize: number;
};
declare const ResourceSummaryComputed: typeof ResourceSummary & {
    request: (dependencies: {
        URL: LH.Artifacts['URL'];
        devtoolsLog: import("../index.js").DevtoolsLog;
        budgets: LH.Util.ImmutableObject<LH.Budget[] | null>;
    }, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<Record<import("../../types/lhr/budget.js").default.ResourceType, ResourceEntry>>;
};
/** @typedef {{count: number, resourceSize: number, transferSize: number}} ResourceEntry */
declare class ResourceSummary {
    /**
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {LH.Budget.ResourceType}
     */
    static determineResourceType(record: LH.Artifacts.NetworkRequest): LH.Budget.ResourceType;
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {LH.Artifacts.URL} URLArtifact
     * @param {LH.Util.ImmutableObject<LH.Budget[]|null>} budgets
     * @param {LH.Artifacts.EntityClassification} classifiedEntities
     * @return {Record<LH.Budget.ResourceType, ResourceEntry>}
     */
    static summarize(networkRecords: Array<LH.Artifacts.NetworkRequest>, URLArtifact: LH.Artifacts.URL, budgets: LH.Util.ImmutableObject<LH.Budget[] | null>, classifiedEntities: LH.Artifacts.EntityClassification): Record<LH.Budget.ResourceType, ResourceEntry>;
    /**
     * @param {{URL: LH.Artifacts['URL'], devtoolsLog: LH.DevtoolsLog, budgets: LH.Util.ImmutableObject<LH.Budget[]|null>}} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<Record<LH.Budget.ResourceType,ResourceEntry>>}
     */
    static compute_(data: {
        URL: LH.Artifacts['URL'];
        devtoolsLog: import("../index.js").DevtoolsLog;
        budgets: LH.Util.ImmutableObject<LH.Budget[] | null>;
    }, context: LH.Artifacts.ComputedContext): Promise<Record<LH.Budget.ResourceType, ResourceEntry>>;
}
import { Budget } from '../config/budget.js';
import { NetworkRequest } from '../lib/network-request.js';
import { EntityClassification } from './entity-classification.js';
//# sourceMappingURL=resource-summary.d.ts.map