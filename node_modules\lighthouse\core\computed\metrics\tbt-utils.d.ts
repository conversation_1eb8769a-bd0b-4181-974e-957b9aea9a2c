/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export const BLOCKING_TIME_THRESHOLD: 50;
/**
 * @param {Array<{start: number, end: number, duration: number}>} topLevelEvents
 * @param {number} startTimeMs
 * @param {number} endTimeMs
 * @return {number}
 */
export function calculateSumOfBlockingTime(topLevelEvents: Array<{
    start: number;
    end: number;
    duration: number;
}>, startTimeMs: number, endTimeMs: number): number;
/**
 * For TBT, We only want to consider tasks that fall in our time range
 * - FCP and TTI for navigation mode
 * - Trace start and trace end for timespan mode
 *
 * FCP is picked as `startTimeMs` because there is little risk of user input happening
 * before FCP so Long Queuing Qelay regions do not harm user experience. Developers should be
 * optimizing to reach FCP as fast as possible without having to worry about task lengths.
 *
 * TTI is picked as `endTimeMs` because we want a well defined end point for page load.
 *
 * @param {{start: number, end: number, duration: number}} event
 * @param {number} startTimeMs Should be FCP in navigation mode and the trace start time in timespan mode
 * @param {number} endTimeMs Should be TTI in navigation mode and the trace end time in timespan mode
 * @param {{start: number, end: number, duration: number}} [topLevelEvent] Leave unset if `event` is top level. Has no effect if `event` has the same duration as `topLevelEvent`.
 * @return {number}
 */
export function calculateTbtImpactForEvent(event: {
    start: number;
    end: number;
    duration: number;
}, startTimeMs: number, endTimeMs: number, topLevelEvent?: {
    start: number;
    end: number;
    duration: number;
} | undefined): number;
//# sourceMappingURL=tbt-utils.d.ts.map