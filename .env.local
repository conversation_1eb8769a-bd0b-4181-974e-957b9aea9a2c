# Local Development Environment Variables
# Copy from .env.example and fill with your actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://zeieudvbhlrlnfkwejoh.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDk1MDEsImV4cCI6MjA2Njg4NTUwMX0.sOImH-XXxxVjjUZhWwYt6KK6dpfCBK2wvT2rnPmlC50
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SUPABASE_PROJECT_ID=zeieudvbhlrlnfkwejoh

# Application Configuration
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Sistema de Atención Ciudadana - Chía"
NEXT_PUBLIC_APP_DESCRIPTION="Plataforma digital para la atención ciudadana del municipio de Chía"

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Development Tools
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
