/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { FunctionComponent } from 'preact';
/**
 * The div should behave like a JSX <>...</>. This still allows us to identify "rows" with CSS selectors.
 */
declare const SummaryFlowStep: FunctionComponent<{
    lhr: LH.Result;
    label: string;
    hashIndex: number;
}>;
declare const SummaryHeader: FunctionComponent;
declare const Summary: FunctionComponent;
export { SummaryFlowStep, SummaryHeader, Summary, };
//# sourceMappingURL=summary.d.ts.map