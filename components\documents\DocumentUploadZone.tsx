'use client'

import { useState, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  X,
  Upload,
  File,
  FileText,
  Image,
  AlertCircle,
  CheckCircle,
  Plus,
  Trash2
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface DocumentUploadZoneProps {
  user: any
  procedures: any[]
  onClose: () => void
}

interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  error?: string
}

export function DocumentUploadZone({
  user,
  procedures,
  onClose
}: DocumentUploadZoneProps) {
  const [selectedProcedure, setSelectedProcedure] = useState<string>('')
  const [files, setFiles] = useState<UploadFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supabase = createClient()

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files))
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files))
    }
  }

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadFile[] = fileList.map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending'
    }))

    // Validate files
    const validFiles = newFiles.filter(uploadFile => {
      const file = uploadFile.file
      
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        uploadFile.status = 'error'
        uploadFile.error = 'El archivo es demasiado grande (máximo 10MB)'
        return true // Include to show error
      }

      // Check file type
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'image/jpg'
      ]
      
      if (!allowedTypes.includes(file.type)) {
        uploadFile.status = 'error'
        uploadFile.error = 'Tipo de archivo no permitido'
        return true // Include to show error
      }

      return true
    })

    setFiles(prev => [...prev, ...validFiles])
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const uploadFiles = async () => {
    if (!selectedProcedure) {
      alert('Por favor selecciona un trámite')
      return
    }

    const validFiles = files.filter(f => f.status === 'pending')
    if (validFiles.length === 0) {
      alert('No hay archivos válidos para subir')
      return
    }

    setUploading(true)

    try {
      const procedure = procedures.find(p => p.id === selectedProcedure)
      if (!procedure) throw new Error('Trámite no encontrado')

      // Upload files one by one
      for (const uploadFile of validFiles) {
        try {
          // Update status to uploading
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'uploading' as const, progress: 0 }
              : f
          ))

          // Generate unique file path
          const fileExt = uploadFile.file.name.split('.').pop()
          const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`
          const filePath = `${user.id}/${procedure.id}/${fileName}`

          // Upload file
          const { data, error } = await supabase.storage
            .from('documents')
            .upload(filePath, uploadFile.file)

          if (error) throw error

          // Update progress to 100%
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, progress: 100, status: 'completed' as const }
              : f
          ))

          // Update procedure attachments
          const currentAttachments = procedure.attachments || []
          const updatedAttachments = [...currentAttachments, filePath]

          const { error: updateError } = await supabase
            .from('citizen_procedures')
            .update({ 
              attachments: updatedAttachments,
              updated_at: new Date().toISOString()
            })
            .eq('id', procedure.id)

          if (updateError) throw updateError

        } catch (error: any) {
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { 
                  ...f, 
                  status: 'error' as const, 
                  error: error.message || 'Error al subir archivo'
                }
              : f
          ))
        }
      }

      // Create notification
      await supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          title: 'Documentos subidos',
          message: `Se han subido ${validFiles.length} documento(s) al trámite ${procedure.reference_number}`,
          type: 'document_uploaded',
          related_id: procedure.id,
          is_read: false
        })

    } catch (error: any) {
      console.error('Error uploading files:', error)
    } finally {
      setUploading(false)
    }
  }

  const getFileIcon = (file: File) => {
    if (file.type.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-600" />
    } else if (file.type.includes('image')) {
      return <Image className="h-8 w-8 text-green-600" />
    } else {
      return <File className="h-8 w-8 text-blue-600" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const completedFiles = files.filter(f => f.status === 'completed').length
  const totalFiles = files.length

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Subir Documentos
            </h2>
            <p className="text-sm text-gray-500">
              Selecciona un trámite y sube los documentos necesarios
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)] space-y-6">
          {/* Procedure Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Seleccionar Trámite</CardTitle>
              <CardDescription>
                Elige el trámite al que pertenecen los documentos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Select value={selectedProcedure} onValueChange={setSelectedProcedure}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un trámite..." />
                </SelectTrigger>
                <SelectContent>
                  {procedures.map((procedure) => (
                    <SelectItem key={procedure.id} value={procedure.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{procedure.procedure.name}</span>
                        <Badge variant="outline" className="ml-2">
                          {procedure.reference_number}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Upload Area */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Subir Archivos</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Arrastra archivos aquí o haz clic para seleccionar
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Formatos: PDF, DOC, DOCX, JPG, PNG (máximo 10MB por archivo)
                </p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Seleccionar Archivos
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={handleFileInput}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* File List */}
          {files.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Archivos Seleccionados ({files.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {files.map((uploadFile) => (
                    <div key={uploadFile.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      {getFileIcon(uploadFile.file)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {uploadFile.file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(uploadFile.file.size)}
                        </p>
                        {uploadFile.status === 'uploading' && (
                          <Progress value={uploadFile.progress} className="mt-1" />
                        )}
                        {uploadFile.error && (
                          <p className="text-xs text-red-600 mt-1">
                            {uploadFile.error}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {uploadFile.status === 'completed' && (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        )}
                        {uploadFile.status === 'error' && (
                          <AlertCircle className="h-5 w-5 text-red-600" />
                        )}
                        {uploadFile.status === 'pending' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(uploadFile.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {completedFiles > 0 && (
              <span>{completedFiles} de {totalFiles} archivos subidos</span>
            )}
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              {completedFiles > 0 ? 'Cerrar' : 'Cancelar'}
            </Button>
            {files.length > 0 && (
              <Button 
                onClick={uploadFiles}
                disabled={uploading || !selectedProcedure || files.filter(f => f.status === 'pending').length === 0}
              >
                {uploading ? 'Subiendo...' : 'Subir Archivos'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
