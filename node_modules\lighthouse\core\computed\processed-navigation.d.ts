export { ProcessedNavigationComputed as ProcessedNavigation };
declare const ProcessedNavigationComputed: typeof ProcessedNavigation & {
    request: (dependencies: import("../index.js").Artifacts.ProcessedTrace | import("../index.js").Trace, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../index.js").Artifacts.ProcessedNavigation>;
};
declare class ProcessedNavigation {
    /**
     * @param {LH.Trace | LH.Artifacts.ProcessedTrace} traceOrProcessedTrace
     * @return {traceOrProcessedTrace is LH.Artifacts.ProcessedTrace}
     */
    static isProcessedTrace(traceOrProcessedTrace: LH.Trace | LH.Artifacts.ProcessedTrace): traceOrProcessedTrace is import("../index.js").Artifacts.ProcessedTrace;
    /**
     * @param {LH.Trace | LH.Artifacts.ProcessedTrace} traceOrProcessedTrace
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.ProcessedNavigation>}
     */
    static compute_(traceOrProcessedTrace: LH.Trace | LH.Artifacts.ProcessedTrace, context: LH.Artifacts.ComputedContext): Promise<LH.Artifacts.ProcessedNavigation>;
}
import { ProcessedTrace } from './processed-trace.js';
//# sourceMappingURL=processed-navigation.d.ts.map