"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "color": "#eaeaea",
    "background": "#000",
    "padding": "0.5em"
  },
  "hljs-subst": {
    "color": "#eaeaea"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-builtin-name": {
    "color": "#eaeaea"
  },
  "hljs-type": {
    "color": "#eaeaea"
  },
  "hljs-params": {
    "color": "#da0000"
  },
  "hljs-literal": {
    "color": "#ff0000",
    "fontWeight": "bolder"
  },
  "hljs-number": {
    "color": "#ff0000",
    "fontWeight": "bolder"
  },
  "hljs-name": {
    "color": "#ff0000",
    "fontWeight": "bolder"
  },
  "hljs-comment": {
    "color": "#969896"
  },
  "hljs-selector-id": {
    "color": "#00ffff"
  },
  "hljs-quote": {
    "color": "#00ffff"
  },
  "hljs-template-variable": {
    "color": "#00ffff",
    "fontWeight": "bold"
  },
  "hljs-variable": {
    "color": "#00ffff",
    "fontWeight": "bold"
  },
  "hljs-title": {
    "color": "#00ffff",
    "fontWeight": "bold"
  },
  "hljs-selector-class": {
    "color": "#fff000"
  },
  "hljs-keyword": {
    "color": "#fff000"
  },
  "hljs-symbol": {
    "color": "#fff000"
  },
  "hljs-string": {
    "color": "#00ff00"
  },
  "hljs-bullet": {
    "color": "#00ff00"
  },
  "hljs-tag": {
    "color": "#000fff"
  },
  "hljs-section": {
    "color": "#000fff"
  },
  "hljs-selector-tag": {
    "color": "#000fff",
    "fontWeight": "bold"
  },
  "hljs-attribute": {
    "color": "#ff00ff"
  },
  "hljs-built_in": {
    "color": "#ff00ff"
  },
  "hljs-regexp": {
    "color": "#ff00ff"
  },
  "hljs-link": {
    "color": "#ff00ff"
  },
  "hljs-meta": {
    "color": "#fff",
    "fontWeight": "bolder"
  }
};