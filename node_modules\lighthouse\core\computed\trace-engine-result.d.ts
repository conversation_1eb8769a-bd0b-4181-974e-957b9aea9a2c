export { TraceEngineResultComputed as Trace<PERSON>ngineResult };
declare const TraceEngineResultComputed: typeof TraceEngineResult & {
    request: (dependencies: {
        trace: LH.Trace;
    }, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<Readonly<import("@paulirish/trace_engine/models/trace/handlers/types.js").EnabledHandlerDataWithMeta<typeof import("@paulirish/trace_engine/models/trace/handlers/ModelHandlers.js")>>>;
};
/**
 * @fileoverview Processes trace with the shared trace engine.
 */
declare class TraceEngineResult {
    /**
     * @param {LH.TraceEvent[]} traceEvents
     */
    static runTraceEngine(traceEvents: LH.TraceEvent[]): Promise<Readonly<import("@paulirish/trace_engine/models/trace/handlers/types.js").EnabledHandlerDataWithMeta<typeof import("@paulirish/trace_engine/models/trace/handlers/ModelHandlers.js")>>>;
    /**
     * @param {{trace: LH.Trace}} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.TraceEngineResult>}
     */
    static compute_(data: {
        trace: LH.Trace;
    }, context: LH.Artifacts.ComputedContext): Promise<LH.Artifacts.TraceEngineResult>;
}
//# sourceMappingURL=trace-engine-result.d.ts.map