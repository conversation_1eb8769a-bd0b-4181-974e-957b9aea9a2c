import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Input } from '@/components/ui/input'

describe('Input Component', () => {
  it('renders input with placeholder', () => {
    render(<Input placeholder="Enter text" />)
    
    const input = screen.getByPlaceholderText('Enter text')
    expect(input).toBeInTheDocument()
  })

  it('handles text input correctly', async () => {
    const user = userEvent.setup()
    render(<Input placeholder="Type here" />)
    
    const input = screen.getByPlaceholderText('Type here')
    await user.type(input, 'Hello World')
    
    expect(input).toHaveValue('Hello World')
  })

  it('handles onChange events', async () => {
    const handleChange = jest.fn()
    const user = userEvent.setup()
    
    render(<Input onChange={handleChange} placeholder="Test input" />)
    
    const input = screen.getByPlaceholderText('Test input')
    await user.type(input, 'test')
    
    expect(handleChange).toHaveBeenCalled()
    expect(handleChange).toHaveBeenCalledTimes(4) // One for each character
  })

  it('supports different input types', () => {
    const { rerender } = render(<Input type="text" placeholder="Text" />)
    
    let input = screen.getByPlaceholderText('Text')
    expect(input).toHaveAttribute('type', 'text')
    
    rerender(<Input type="email" placeholder="Email" />)
    input = screen.getByPlaceholderText('Email')
    expect(input).toHaveAttribute('type', 'email')
    
    rerender(<Input type="password" placeholder="Password" />)
    input = screen.getByPlaceholderText('Password')
    expect(input).toHaveAttribute('type', 'password')
    
    rerender(<Input type="number" placeholder="Number" />)
    input = screen.getByPlaceholderText('Number')
    expect(input).toHaveAttribute('type', 'number')
  })

  it('handles disabled state', () => {
    render(<Input disabled placeholder="Disabled input" />)
    
    const input = screen.getByPlaceholderText('Disabled input')
    expect(input).toBeDisabled()
    expect(input).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50')
  })

  it('supports custom className', () => {
    render(<Input className="custom-class" placeholder="Custom" />)
    
    const input = screen.getByPlaceholderText('Custom')
    expect(input).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLInputElement>()
    render(<Input ref={ref} placeholder="Ref test" />)
    
    expect(ref.current).toBeInstanceOf(HTMLInputElement)
  })

  it('supports value and defaultValue', () => {
    const { rerender } = render(<Input value="controlled" onChange={() => {}} />)
    
    let input = screen.getByDisplayValue('controlled')
    expect(input).toHaveValue('controlled')
    
    rerender(<Input defaultValue="uncontrolled" />)
    input = screen.getByDisplayValue('uncontrolled')
    expect(input).toHaveValue('uncontrolled')
  })

  it('handles focus and blur events', () => {
    const handleFocus = jest.fn()
    const handleBlur = jest.fn()
    
    render(
      <Input
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder="Focus test"
      />
    )
    
    const input = screen.getByPlaceholderText('Focus test')
    
    fireEvent.focus(input)
    expect(handleFocus).toHaveBeenCalled()
    
    fireEvent.blur(input)
    expect(handleBlur).toHaveBeenCalled()
  })

  it('supports required attribute', () => {
    render(<Input required placeholder="Required input" />)
    
    const input = screen.getByPlaceholderText('Required input')
    expect(input).toBeRequired()
  })

  it('supports min and max attributes for number inputs', () => {
    render(
      <Input
        type="number"
        min={0}
        max={100}
        placeholder="Number input"
      />
    )
    
    const input = screen.getByPlaceholderText('Number input')
    expect(input).toHaveAttribute('min', '0')
    expect(input).toHaveAttribute('max', '100')
  })

  it('supports maxLength attribute', () => {
    render(<Input maxLength={10} placeholder="Max length" />)
    
    const input = screen.getByPlaceholderText('Max length')
    expect(input).toHaveAttribute('maxLength', '10')
  })

  it('is accessible with proper ARIA attributes', () => {
    render(
      <Input
        aria-label="Accessible input"
        aria-describedby="help-text"
        placeholder="Accessible"
      />
    )
    
    const input = screen.getByLabelText('Accessible input')
    expect(input).toBeInTheDocument()
    expect(input).toHaveAttribute('aria-describedby', 'help-text')
  })

  it('supports keyboard navigation', () => {
    render(<Input placeholder="Keyboard test" />)
    
    const input = screen.getByPlaceholderText('Keyboard test')
    
    // Focus the input
    input.focus()
    expect(input).toHaveFocus()
    
    // Test Tab key (should maintain focus until tabbed away)
    fireEvent.keyDown(input, { key: 'Tab', code: 'Tab' })
    // Input should still be focusable
    expect(input).toBeInTheDocument()
  })

  it('handles form integration', () => {
    const handleSubmit = jest.fn((e) => {
      e.preventDefault()
      const formData = new FormData(e.target)
      expect(formData.get('testInput')).toBe('test value')
    })
    
    render(
      <form onSubmit={handleSubmit}>
        <Input name="testInput" defaultValue="test value" />
        <button type="submit">Submit</button>
      </form>
    )
    
    const submitButton = screen.getByRole('button', { name: 'Submit' })
    fireEvent.click(submitButton)
    
    expect(handleSubmit).toHaveBeenCalled()
  })

  it('applies focus styles correctly', () => {
    render(<Input placeholder="Focus styles" />)
    
    const input = screen.getByPlaceholderText('Focus styles')
    expect(input).toHaveClass('focus-visible:ring-chia-blue-500')
  })

  it('handles readonly state', () => {
    render(<Input readOnly value="readonly" placeholder="Readonly" />)
    
    const input = screen.getByDisplayValue('readonly')
    expect(input).toHaveAttribute('readonly')
  })

  it('supports autoComplete attribute', () => {
    render(<Input autoComplete="email" placeholder="Email" />)
    
    const input = screen.getByPlaceholderText('Email')
    expect(input).toHaveAttribute('autoComplete', 'email')
  })

  it('handles file input type', () => {
    render(<Input type="file" accept=".pdf,.doc" />)
    
    const input = screen.getByRole('textbox', { hidden: true }) || 
                  document.querySelector('input[type="file"]')
    expect(input).toHaveAttribute('type', 'file')
    expect(input).toHaveAttribute('accept', '.pdf,.doc')
  })

  it('combines multiple props correctly', async () => {
    const handleChange = jest.fn()
    const user = userEvent.setup()
    
    render(
      <Input
        type="email"
        placeholder="Email address"
        className="custom-email"
        required
        maxLength={50}
        onChange={handleChange}
        aria-label="Email input"
      />
    )
    
    const input = screen.getByLabelText('Email input')
    expect(input).toBeInTheDocument()
    expect(input).toHaveAttribute('type', 'email')
    expect(input).toHaveClass('custom-email')
    expect(input).toBeRequired()
    expect(input).toHaveAttribute('maxLength', '50')
    
    await user.type(input, '<EMAIL>')
    expect(handleChange).toHaveBeenCalled()
  })
})
