export default {
  "hljs-comment": {
    "color": "#969896"
  },
  "hljs-quote": {
    "color": "#969896"
  },
  "hljs-variable": {
    "color": "#cc6666"
  },
  "hljs-template-variable": {
    "color": "#cc6666"
  },
  "hljs-tag": {
    "color": "#cc6666"
  },
  "hljs-name": {
    "color": "#cc6666"
  },
  "hljs-selector-id": {
    "color": "#cc6666"
  },
  "hljs-selector-class": {
    "color": "#cc6666"
  },
  "hljs-regexp": {
    "color": "#cc6666"
  },
  "hljs-deletion": {
    "color": "#cc6666"
  },
  "hljs-number": {
    "color": "#de935f"
  },
  "hljs-built_in": {
    "color": "#de935f"
  },
  "hljs-builtin-name": {
    "color": "#de935f"
  },
  "hljs-literal": {
    "color": "#de935f"
  },
  "hljs-type": {
    "color": "#de935f"
  },
  "hljs-params": {
    "color": "#de935f"
  },
  "hljs-meta": {
    "color": "#de935f"
  },
  "hljs-link": {
    "color": "#de935f"
  },
  "hljs-attribute": {
    "color": "#f0c674"
  },
  "hljs-string": {
    "color": "#b5bd68"
  },
  "hljs-symbol": {
    "color": "#b5bd68"
  },
  "hljs-bullet": {
    "color": "#b5bd68"
  },
  "hljs-addition": {
    "color": "#b5bd68"
  },
  "hljs-title": {
    "color": "#81a2be"
  },
  "hljs-section": {
    "color": "#81a2be"
  },
  "hljs-keyword": {
    "color": "#b294bb"
  },
  "hljs-selector-tag": {
    "color": "#b294bb"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "#1d1f21",
    "color": "#c5c8c6",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};