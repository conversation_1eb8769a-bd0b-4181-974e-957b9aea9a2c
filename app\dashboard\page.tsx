import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  MessageSquare, 
  Bell, 
  User, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

export default async function DashboardPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  // Get user's recent procedures
  const supabase = createClient()
  const { data: recentProcedures } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(name),
      status:procedure_statuses(name, display_name, color)
    `)
    .eq('citizen_id', user.id)
    .order('created_at', { ascending: false })
    .limit(5)

  // Get user's unread notifications count
  const { count: unreadNotifications } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .eq('is_read', false)

  // Get procedure statistics
  const { count: totalProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)

  const { count: completedProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)
    .eq('status_id', 'completed')

  const { count: pendingProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)
    .eq('status_id', 'pending')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Bienvenido, {profile.full_name}
              </h1>
              <p className="text-gray-600">
                Panel de control - Sistema de Atención Ciudadana
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Notificaciones
                {unreadNotifications && unreadNotifications > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {unreadNotifications}
                  </span>
                )}
              </Button>
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 mr-2" />
                Perfil
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalProcedures || 0}</div>
              <p className="text-xs text-muted-foreground">
                Trámites iniciados
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Proceso</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{pendingProcedures || 0}</div>
              <p className="text-xs text-muted-foreground">
                Pendientes de completar
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completados</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{completedProcedures || 0}</div>
              <p className="text-xs text-muted-foreground">
                Trámites finalizados
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Notificaciones</CardTitle>
              <Bell className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{unreadNotifications || 0}</div>
              <p className="text-xs text-muted-foreground">
                Sin leer
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Acciones Rápidas</CardTitle>
              <CardDescription>
                Accede a los servicios más utilizados
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Iniciar Nuevo Trámite
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                Consultar con Asistente IA
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <TrendingUp className="mr-2 h-4 w-4" />
                Ver Estado de Trámites
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Trámites Recientes</CardTitle>
              <CardDescription>
                Últimos trámites iniciados
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentProcedures && recentProcedures.length > 0 ? (
                <div className="space-y-4">
                  {recentProcedures.map((procedure: any) => (
                    <div key={procedure.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium text-sm">
                          {procedure.procedure?.name || 'Trámite'}
                        </p>
                        <p className="text-xs text-gray-500">
                          Ref: {procedure.reference_number}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span 
                          className="px-2 py-1 text-xs rounded-full"
                          style={{ 
                            backgroundColor: procedure.status?.color + '20',
                            color: procedure.status?.color 
                          }}
                        >
                          {procedure.status?.display_name || 'Pendiente'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <p>No tienes trámites iniciados</p>
                  <Button className="mt-4" size="sm">
                    Iniciar Primer Trámite
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="mr-2 h-5 w-5 text-blue-600" />
                Información Importante
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-900">
                    Horarios de Atención
                  </p>
                  <p className="text-sm text-blue-700">
                    Lunes a Viernes: 8:00 AM - 5:00 PM
                  </p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-green-900">
                    Servicios en Línea 24/7
                  </p>
                  <p className="text-sm text-green-700">
                    Disponibles todos los días del año
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contacto y Soporte</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Línea de Atención</p>
                  <p className="text-sm text-gray-600">(601) 123-4567</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
                <div>
                  <p className="text-sm font-medium">Dirección</p>
                  <p className="text-sm text-gray-600">
                    Carrera 11 # 17-25, Chía, Cundinamarca
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
