export class ReportScoring {
    /**
     * Computes the weighted-average of the score of the list of items.
     * @param {Array<{score: number|null, weight: number}>} items
     * @return {number|null}
     */
    static arithmeticMean(items: Array<{
        score: number | null;
        weight: number;
    }>): number | null;
    /**
     * Returns the report JSON object with computed scores.
     * @param {Object<string, LH.Config.Category>} configCategories
     * @param {Object<string, LH.RawIcu<LH.Audit.Result>>} resultsByAuditId
     * @return {Object<string, LH.RawIcu<LH.Result.Category>>}
     */
    static scoreAllCategories(configCategories: {
        [x: string]: LH.Config.Category;
    }, resultsByAuditId: {
        [x: string]: LH.RawIcu<LH.Audit.Result>;
    }): {
        [x: string]: LH.RawIcu<LH.Result.Category>;
    };
}
import { Audit } from './audits/audit.js';
//# sourceMappingURL=scoring.d.ts.map