'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText,
  Image,
  File,
  Download,
  Eye,
  Trash2,
  Share2,
  Calendar,
  Building,
  ExternalLink,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Document {
  id: string
  name: string
  path: string
  procedureId?: string
  procedureName?: string
  referenceNumber?: string
  dependency?: string
  status?: any
  uploadedAt: string
  size?: number
  type: string
}

interface DocumentGridProps {
  documents: Document[]
  compact?: boolean
}

export function DocumentGrid({ documents, compact = false }: DocumentGridProps) {
  const [loading, setLoading] = useState<string | null>(null)
  const supabase = createClient()

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-600" />
      case 'image':
        return <Image className="h-8 w-8 text-green-600" />
      case 'document':
        return <FileText className="h-8 w-8 text-blue-600" />
      default:
        return <File className="h-8 w-8 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'bg-red-100 text-red-800'
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'document':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = async (document: Document) => {
    setLoading(document.id)
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      // Create a temporary link to download the file
      const link = document.createElement('a')
      link.href = data.publicUrl
      link.download = document.name
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
    } finally {
      setLoading(null)
    }
  }

  const handleView = async (document: Document) => {
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      window.open(data.publicUrl, '_blank')
    } catch (error) {
      console.error('Error viewing file:', error)
    }
  }

  const handleShare = async (document: Document) => {
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(document.path)
      
      if (navigator.share) {
        await navigator.share({
          title: document.name,
          text: `Documento: ${document.name}`,
          url: data.publicUrl
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(data.publicUrl)
        // You could show a toast notification here
      }
    } catch (error) {
      console.error('Error sharing file:', error)
    }
  }

  if (documents.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <File className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay documentos
          </h3>
          <p className="text-gray-500">
            No se encontraron documentos que coincidan con los filtros seleccionados
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`grid gap-4 ${
      compact 
        ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' 
        : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
    }`}>
      {documents.map((document) => (
        <Card key={document.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* File Icon and Type */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getFileIcon(document.type)}
                  <Badge className={getFileTypeColor(document.type)}>
                    {document.type.toUpperCase()}
                  </Badge>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleView(document)}>
                      <Eye className="h-4 w-4 mr-2" />
                      Ver
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDownload(document)}
                      disabled={loading === document.id}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Descargar
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleShare(document)}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Compartir
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* File Name */}
              <div>
                <h3 className="font-medium text-gray-900 truncate" title={document.name}>
                  {document.name}
                </h3>
                {document.size && (
                  <p className="text-xs text-gray-500">
                    {formatFileSize(document.size)}
                  </p>
                )}
              </div>

              {/* Procedure Info (if available) */}
              {document.procedureName && !compact && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-700 truncate">
                    {document.procedureName}
                  </p>
                  {document.referenceNumber && (
                    <p className="text-xs text-gray-500">
                      {document.referenceNumber}
                    </p>
                  )}
                  {document.dependency && (
                    <p className="text-xs text-gray-500 flex items-center">
                      <Building className="h-3 w-3 mr-1" />
                      {document.dependency}
                    </p>
                  )}
                </div>
              )}

              {/* Status (if available) */}
              {document.status && !compact && (
                <Badge 
                  style={{ backgroundColor: document.status.color }}
                  className="text-white text-xs"
                >
                  {document.status.display_name}
                </Badge>
              )}

              {/* Upload Date */}
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(document.uploadedAt)}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleView(document)}
                  className="flex-1"
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Ver
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload(document)}
                  disabled={loading === document.id}
                  className="flex-1"
                >
                  <Download className="h-3 w-3 mr-1" />
                  {loading === document.id ? '...' : 'Descargar'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
